'use client'

import React, { useState, useEffect } from 'react'
import { Page } from '@/lib/page-builder/store'

interface TestResult {
  test: string
  passed: boolean
  details: string
}

const PageManagerTest: React.FC = () => {
  const [pages, setPages] = useState<Page[]>([])
  const [testResults, setTestResults] = useState<TestResult[]>([])
  const [isRunning, setIsRunning] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')

  // Fetch pages from API
  const fetchPages = async () => {
    try {
      const response = await fetch('/api/page-builder/pages')
      if (!response.ok) {
        throw new Error('Failed to fetch pages')
      }
      const data = await response.json()
      const formattedPages: Page[] = data.map((page: any) => ({
        id: page.id.toString(),
        title: page.title,
        slug: page.slug,
        description: page.description,
        metaTitle: page.metaTitle,
        metaDescription: page.metaDescription,
        isPublished: page.isPublished,
        isHomePage: page.isHomePage,
        layout: page.layout || [],
        styles: page.styles || {},
        seoSettings: page.seoSettings || {},
        createdAt: new Date(page.createdAt),
        updatedAt: new Date(page.updatedAt),
        publishedAt: page.publishedAt ? new Date(page.publishedAt) : undefined
      }))
      setPages(formattedPages)
      return formattedPages
    } catch (error) {
      console.error('Error fetching pages:', error)
      return []
    }
  }

  // Filter pages based on search term (same logic as PageManager)
  const filteredPages = pages.filter(page =>
    page.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    page.slug.toLowerCase().includes(searchTerm.toLowerCase()) ||
    page.description?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // Run comprehensive tests
  const runTests = async () => {
    setIsRunning(true)
    const results: TestResult[] = []

    // Test 1: Fetch pages
    const fetchedPages = await fetchPages()
    results.push({
      test: 'Fetch Pages',
      passed: fetchedPages.length > 0,
      details: `Fetched ${fetchedPages.length} pages`
    })

    // Test 2: Check expected pages exist
    const expectedPages = ['home', 'about', 'services', 'contact', 'portfolio', 'blog', 'pricing']
    const foundSlugs = fetchedPages.map(p => p.slug)
    const missingPages = expectedPages.filter(slug => !foundSlugs.includes(slug))
    results.push({
      test: 'Expected Pages Exist',
      passed: missingPages.length === 0,
      details: missingPages.length === 0 ? 'All expected pages found' : `Missing: ${missingPages.join(', ')}`
    })

    // Test 3: Search by title
    const titleSearchResults = fetchedPages.filter(page =>
      page.title.toLowerCase().includes('home')
    )
    results.push({
      test: 'Search by Title',
      passed: titleSearchResults.length > 0,
      details: `Found ${titleSearchResults.length} pages with 'home' in title`
    })

    // Test 4: Search by slug
    const slugSearchResults = fetchedPages.filter(page =>
      page.slug.toLowerCase().includes('about')
    )
    results.push({
      test: 'Search by Slug',
      passed: slugSearchResults.length > 0,
      details: `Found ${slugSearchResults.length} pages with 'about' in slug`
    })

    // Test 5: Search by description
    const descSearchResults = fetchedPages.filter(page =>
      page.description?.toLowerCase().includes('software') || false
    )
    results.push({
      test: 'Search by Description',
      passed: descSearchResults.length > 0,
      details: `Found ${descSearchResults.length} pages with 'software' in description`
    })

    // Test 6: Case insensitive search
    const caseInsensitiveResults = fetchedPages.filter(page =>
      page.title.toLowerCase().includes('TECH'.toLowerCase())
    )
    results.push({
      test: 'Case Insensitive Search',
      passed: caseInsensitiveResults.length > 0,
      details: `Found ${caseInsensitiveResults.length} pages with 'TECH' (case insensitive)`
    })

    // Test 7: Empty search returns all pages
    const emptySearchResults = fetchedPages.filter(page =>
      page.title.toLowerCase().includes('') ||
      page.slug.toLowerCase().includes('') ||
      page.description?.toLowerCase().includes('') || false
    )
    results.push({
      test: 'Empty Search Returns All',
      passed: emptySearchResults.length === fetchedPages.length,
      details: `Empty search returned ${emptySearchResults.length}/${fetchedPages.length} pages`
    })

    // Test 8: No results for non-existent term
    const noResultsSearch = fetchedPages.filter(page =>
      page.title.toLowerCase().includes('nonexistentterm') ||
      page.slug.toLowerCase().includes('nonexistentterm') ||
      page.description?.toLowerCase().includes('nonexistentterm') || false
    )
    results.push({
      test: 'No Results for Non-existent Term',
      passed: noResultsSearch.length === 0,
      details: `Search for 'nonexistentterm' returned ${noResultsSearch.length} pages (expected 0)`
    })

    setTestResults(results)
    setIsRunning(false)
  }

  useEffect(() => {
    fetchPages()
  }, [])

  const passedTests = testResults.filter(r => r.passed).length
  const totalTests = testResults.length

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-6">Page Manager Search Functionality Test</h1>
        
        {/* Test Controls */}
        <div className="mb-6">
          <button
            onClick={runTests}
            disabled={isRunning}
            className={`px-4 py-2 rounded-md font-medium ${
              isRunning
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-blue-600 text-white hover:bg-blue-700'
            }`}
          >
            {isRunning ? 'Running Tests...' : 'Run Tests'}
          </button>
          
          {testResults.length > 0 && (
            <div className="mt-4 p-4 rounded-md bg-gray-50">
              <div className="text-lg font-medium">
                Test Results: {passedTests}/{totalTests} passed
              </div>
              <div className={`text-sm ${passedTests === totalTests ? 'text-green-600' : 'text-red-600'}`}>
                {passedTests === totalTests ? '✅ All tests passed!' : '❌ Some tests failed'}
              </div>
            </div>
          )}
        </div>

        {/* Live Search Test */}
        <div className="mb-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-3">Live Search Test</h2>
          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Search Term:
              </label>
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Type to search pages..."
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div className="text-sm text-gray-600">
              Showing {filteredPages.length} of {pages.length} pages
            </div>
            <div className="max-h-40 overflow-y-auto border border-gray-200 rounded-md">
              {filteredPages.map(page => (
                <div key={page.id} className="p-2 border-b border-gray-100 last:border-b-0">
                  <div className="font-medium">{page.title}</div>
                  <div className="text-sm text-gray-500">/{page.slug}</div>
                  {page.description && (
                    <div className="text-xs text-gray-400">{page.description}</div>
                  )}
                </div>
              ))}
              {filteredPages.length === 0 && searchTerm && (
                <div className="p-4 text-center text-gray-500">
                  No pages found for "{searchTerm}"
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Test Results */}
        {testResults.length > 0 && (
          <div>
            <h2 className="text-lg font-semibold text-gray-900 mb-3">Detailed Test Results</h2>
            <div className="space-y-2">
              {testResults.map((result, index) => (
                <div
                  key={index}
                  className={`p-3 rounded-md border ${
                    result.passed
                      ? 'bg-green-50 border-green-200 text-green-800'
                      : 'bg-red-50 border-red-200 text-red-800'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <span className="font-medium">
                      {result.passed ? '✅' : '❌'} {result.test}
                    </span>
                  </div>
                  <div className="text-sm mt-1">{result.details}</div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Pages Summary */}
        <div className="mt-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-3">Available Pages ({pages.length})</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            {pages.map(page => (
              <div key={page.id} className="p-3 border border-gray-200 rounded-md">
                <div className="font-medium">{page.title}</div>
                <div className="text-sm text-gray-500">/{page.slug}</div>
                <div className="flex items-center space-x-2 mt-1">
                  {page.isPublished ? (
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      Published
                    </span>
                  ) : (
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                      Draft
                    </span>
                  )}
                  {page.isHomePage && (
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      Home
                    </span>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

export default PageManagerTest
